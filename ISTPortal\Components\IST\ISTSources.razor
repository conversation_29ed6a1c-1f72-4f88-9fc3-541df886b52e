﻿@page "/planning/istsource"
@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Notifications
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
@inject Syncfusion.Blazor.Popups.SfDialogService sfDialogService
@inject NavigationManager NavigationManager
@inject ISTDataService istService
@inject IJSRuntime JS

@rendermode InteractiveServer

<style>
    .componentWidth {
        width: -webkit-fill-available;
    }

    label {
        padding-left: 2px;
    }

    .e-grid td.e-active {
        background: powderblue !important;
    }

    .istMsg {
        font-size: 14px;
        color: firebrick;
        padding-left: 25px;
        padding-top: 5px;
    }

    .gridlbl {
        font-size: 12px;
        font-weight: 500;
    }

    .tbllbl {
        font-size: small;
        font-weight: 400;
    }

    .closeBoxdiv {
        border: 1px solid orange;
        border-radius: 8px;
        margin: 10px 0px 10px 0px;
        height: 65px;
        font-weight: 500;
        padding: 2px 0px 5px 10px;
        background-color: lightcyan;
    }

</style>

<FluentBreadcrumb>
    <FluentBreadcrumbItem Href="/home">
        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" Color="@Color.Neutral" Slot="start" />
    </FluentBreadcrumbItem>
    <FluentBreadcrumbItem Href="/planning/istsource">
        Sending Store
    </FluentBreadcrumbItem>
</FluentBreadcrumb>


@* <div class="row mt-2">
    <div class="col-2">
        <FluentDatePicker Label="Start Date" AriaLabel="Start Date" Class="componentWidth" @bind-Value="@filter.fromDate" />
    </div>
    <div class="col-2">
        <FluentDatePicker Label="End Date" Class="componentWidth" AriaLabel="End Date" @bind-Value="@filter.toDate" />
    </div>
    <div class="col-md-3">
        <FluentTextField Class="componentWidth" @bind-Value="filter.ISTCode" Label="IST Code"></FluentTextField>
    </div>
</div>


<FluentStack HorizontalGap="15" Class="mt-2 mb-2">
    <FluentButton IconStart="@(new Icons.Regular.Size16.Search())"
                  BackgroundColor="#00c853" Color="white">
        Search
    </FluentButton>
</FluentStack> *@

<SfToast @ref="toastObj"></SfToast>


<SfGrid @ref="dgISTSrc"
        DataSource="@istList"
        AllowFiltering="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridTemplates>
        <DetailTemplate>
            @{
                if (context is ISTDTO ist)
                {
                    if (ist.ISTStatusId == 2) // SENT
                    {
                        <div class="istMsg">
                            You will not be able to assign a TO No. unless you have accepted or agreed,
                            For further action, click the <SfButton CssClass="e-small e-primary e-flat" IconCss="e-icons e-edit" OnClick="@(() => viewIST(ist))">
                                View
                            </SfButton> button
                        </div>
                    }
                    else if (ist.ISTStatusId == 4) // REJECTED
                    {

                    }
                    else
                    {
                        if (ist.istStoreBatch.Any())
                        {
                            <table class="table-info table-bordered table-sm tbllbl">
                                <thead>
                                    <tr>
                                        <td scope="col" style="width:170px;">TO #</td>
                                        <td scope="col">Status</td>
                                        <td scope="col">Created By</td>
                                        <td scope="col">Created Date</td>
                                        <td scope="col">Total Items</td>
                                        <td scope="col">Total Qty.</td>
                                        @* <td scope="col">TO Created</td>
                                         <td scope="col">TO Shipped</td>
                                         <td scope="col">TO Received</td>
                                        <td scope="col">Modified By</td>
                                        <td scope="col">Modified Date</td>
                                        <td scope="col"></td>*@
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in ist.istStoreBatch)
                                    {
                                        <tr>
                                            <td scope="col" style="width:170px;">@item.StoreBatchTonumber</td>
                                            <td scope="col">
                                                @if (item.storeTOStatus == "DRAFT")
                                                {
                                                    <FluentBadge Appearance="Appearance.Neutral" Fill="highlight" BackgroundColor="#ffd800;" Color="#000">@item.storeTOStatus</FluentBadge>
                                                }
                                                else
                                                {
                                                    <FluentBadge Appearance="Appearance.Accent"> @item.storeTOStatus </FluentBadge>
                                                }
                                            </td>
                                            <td scope="col">@item.StoreBatchCreatedByString</td>
                                            <td scope="col">
                                                @item.StoreBatchCreatedDate!.Value.ToString("dd-MMM-yy HH:mm")
                                            </td>
                                            <td scope="col">@item.TotalItems</td>
                                            <td scope="col">@item.TotalQty</td>
                                            @*  <td scope="col">
                                                @item.TOCreatedDateAX!.Value.ToString("dd-MMM-yy HH:mm")
                                            </td>
                                            <td scope="col">
                                                @item.TOShippedDateAX!.Value.ToString("dd-MMM-yy HH:mm")
                                            </td>
                                            <td scope="col">
                                                @item.TOReceivedDateAX!.Value.ToString("dd-MMM-yy HH:mm")
                                            </td>
                                            <td scope="col">@item.StoreBatchModifiedByString</td>
                                            <td scope="col">
                                                @item.StoreBatchModifiedDate!.Value.ToString("dd-MMM-yy HH:mm")
                                            </td>
                                           <td scope="col">
                                                @{
                                                    <SfButton Disabled="@(item.storeTOStatus == "DRAFT" ? false : true )" CssClass="e-small e-danger e-flat" IconCss="e-icons e-delete"
                                                    OnClick="@(() => removeTOData(ist,@item.StoreBatchId))">
                                                        Remove
                                                    </SfButton>
                                                    // <SfButton CssClass="e-small e-success e-flat" IconCss="e-icons e-user-defined">
                                                    //     View
                                                    // </SfButton>
                                                    <SfButton Disabled="@(item.storeTOStatus == "DRAFT" ? false : true )" CssClass="e-small e-info e-flat" IconCss="e-icons e-hand-gestures"
                                                    OnClick="@(() => dispatchedToItems(ist,@item.StoreBatchTonumber,@item.StoreBatchId))">
                                                        Dispatched
                                                    </SfButton>
                                                }
                                            </td>*@
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        }
                        else
                        {
                            <div class="istMsg">
                                You have accepted it but have not assigned TO Number.
                            </div>
                        }
                    }
                }
            }
        </DetailTemplate>
    </GridTemplates>
    <GridColumns>
        <GridColumn Field=@nameof(ISTDTO.IstCode) HeaderText="IST Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.IstTitle) HeaderText="IST Title" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.fromStore) HeaderText="Source" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.toStore) HeaderText="Destination" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.ISTStatus) HeaderText="Status" TextAlign="TextAlign.Center" AutoFit="true" AllowFiltering="false">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <FluentBadge Appearance="Appearance.Accent">@ctx.ISTStatus</FluentBadge>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Items" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Items : @ctx.TotalItems</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Total Qty. : @ctx.TotalQty</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText="Log" AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Created : @ctx.IstCreatedByString , @ctx.IstCreatedDate!.Value.ToString("dd-MMM-yy HH:mm")</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <span class="gridlbl">Modified : @ctx.IstModifiedByString , @ctx.IstModifiedDate!.Value.ToString("dd-MMM-yy HH:mm")</span>
                        </div>
                    </div>
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText=" " AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    if (ctx.ISTStatusId == 3 || ctx.ISTStatusId == 5)
                    {
                        <SfButton CssClass="e-small e-success e-filled" IconCss="e-icons e-user-defined" OnClick="@(() =>assignTO(ctx))">
                            Assign TO #
                        </SfButton>
                    }
                }
            </Template>
        </GridColumn>
        <GridColumn HeaderText=" " AutoFit="true">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    if (ctx.ISTStatusId == 5)
                    {
                        <SfButton CssClass="e-small e-info e-filled" IconCss="e-icons e-hand-gestures" OnClick="(() => markISTClose(ctx))">
                            Close IST
                        </SfButton>

                    }
                }
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>


<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgViewIST">
    <DialogTemplates>
        <Header>VIEW - IST Source</Header>
        <Content>
            <div class="row">
                <div class="col-md">
                    <b>IST Code :</b> @newIstDto.IstCode
                </div>
                <div class="col-md">
                    <b>IST Title :</b> @newIstDto.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newIstDto.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newIstDto.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newIstDto.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newIstDto.istDetailDtos"
                            AllowFiltering="true" Height="220">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemCode) HeaderText="Item Barcode" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemQty) HeaderText="Item Quantity" TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>

            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                @*    <FluentButton IconStart="@(new Icons.Regular.Size16.SaveMultiple())"
                Appearance="Appearance.Accent" OnClick="acceptIST">
                    Accept
                </FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.CalendarCancel())"
                Appearance="Appearance.Accent" OnClick="rejectIST">
                    Reject
                </FluentButton> *@
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgViewIST.HideAsync())">
                    Close
                </FluentButton>
            </FluentStack>
        </Content>
    </DialogTemplates>
</SfDialog>

<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgAssignTo">
    <DialogTemplates>
        <Header>Assign TO No.</Header>
        <Content>
            <div class="row">
                <div class="col-md-10">
                    <FluentTextField Placeholder="TO No." Class="componentWidth" @bind-Value="newIstDto.TONumber"></FluentTextField>
                </div>
                <div class="col-md-1">
                    <FluentButton IconStart="@(new Icons.Regular.Size16.ArrowDownload())"
                                  Appearance="Appearance.Accent" OnClick="fetchTOItems">
                        Fetch
                    </FluentButton>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>IST Code :</b> @newIstDto.IstCode
                </div>
                <div class="col-md">
                    <b>IST Title :</b> @newIstDto.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newIstDto.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newIstDto.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newIstDto.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newIstDto.istDetailDtos"
                            AllowFiltering="true" Height="220">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemCode) HeaderText="Item Code" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemQty) HeaderText="Item Qty." TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.fetchQty) HeaderText="TO Qty." TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemDesc) HeaderText="Desc." TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            @*  <GridColumn HeaderText="TO Qty." Width="100">
                                <Template>
                                    @{
                                        var ctx = (context as ISTDetailDTO);
                                        <FluentNumberField Min="0" Step="1" ReadOnly @bind-Value="ctx!.fetchQty" />
                                    }
                                </Template>
                            </GridColumn>
                             <GridColumn HeaderText="TO Remarks" Width="250">
                                <Template>
                                    @{
                                        var ctx = (context as ISTDetailDTO);
                                        <FluentTextField Class="componentWidth" @bind-Value="ctx!.TORemarks"></FluentTextField>
                                    }
                                </Template>
                            </GridColumn> *@
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                @* <FluentButton IconStart="@(new Icons.Regular.Size16.SaveMultiple())"
                Appearance="Appearance.Accent" OnClick="@(() => saveTOItems("DRAFT"))">
                    Save
                </FluentButton> *@
                <FluentButton IconStart="@(new Icons.Regular.Size16.Send())"
                              Appearance="Appearance.Accent" OnClick="@(() => saveTOItems("DISPATCHED"))">
                    Save & Dispatched
                </FluentButton>
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgAssignTo.HideAsync())">
                    Close
                </FluentButton>
            </FluentStack>

            @if (closeBox)
            {
                <div class="closeBoxdiv">
                    Kindly click any one option to indicate whether you want to keep 'IST' partially or close it completely.
                    <FluentButton Class="mt-1" IconStart="@(new Icons.Regular.Size16.Send())"
                                  Appearance="Appearance.Accent" OnClick="@(() => closeISTStatus("P"))">
                        Partially
                    </FluentButton>
                    <FluentButton Class="mt-1" IconStart="@(new Icons.Regular.Size16.SendPerson())"
                                  Appearance="Appearance.Accent" OnClick="@(() => closeISTStatus("F"))">
                        Fully Closed
                    </FluentButton>
                    <FluentButton Class="mt-1" IconStart="@(new Icons.Regular.Size16.LockClosed())"
                                  Appearance="Appearance.Accent" OnClick="@(() => closeBox = false)">
                        Cancel
                    </FluentButton>
                </div>
            }


        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private bool closeBox = false;
    private filterForm filter = new();
    private SfGrid<ISTDTO>? dgISTSrc;
    private string loginId = "admin";
    private string ownerType = "SOURCE";
    private List<ISTDTO> istList = new();
    private SfDialog dlgViewIST, dlgAssignTo;
    private ISTDTO newIstDto = new();
    private SfGrid<ISTDetailDTO>? dgISTDetail;

    private async void closeISTStatus(string actionStatus)
    {
        try
        {
            string clickedButtonText = "";
            if (actionStatus == "P")
            {
                clickedButtonText = "Partially";
            }
            else
            {
                clickedButtonText = "Fully Closed";
            }
            var result = await sfDialogService.ConfirmAsync($"You've clicked on a {clickedButtonText}. Do you want to continue?", "Confirmation");
            if (result)
            {
                string pTOStatus = "DISPATCHED";
                await istService.saveTOItems(newIstDto, ownerType, pTOStatus, loginId, actionStatus);
                LoadIST();
                await dlgAssignTo.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void LoadIST()
    {
        istList = await istService.getISTForSource(loginId, filter);
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            LoadIST();
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message.ToString());
        }
    }

    private async void viewIST(ISTDTO pIST)
    {
        try
        {
            newIstDto = new();
            newIstDto = pIST;
            newIstDto.istDetailDtos = await istService.getISTItems(newIstDto.IstId ?? 0);
            await dlgViewIST.ShowAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void rejectIST()
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to reject IST?", "Confirmation");
            if (result)
            {
                await istService.rejectISTSrc(newIstDto.IstId ?? 0, loginId);
                LoadIST();
                await dlgViewIST.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void acceptIST()
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to accept IST?", "Confirmation");
            if (result)
            {
                await istService.acceptISTSrc(newIstDto.IstId ?? 0, loginId);
                LoadIST();
                await dlgViewIST.HideAsync();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void assignTO(ISTDTO pIST)
    {
        try
        {
            closeBox = false;
            newIstDto = new();
            newIstDto = pIST;
            newIstDto.TONumber = "";
            newIstDto.istDetailDtos = await istService.getISTItems(newIstDto.IstId ?? 0);
            await dlgAssignTo.ShowAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private SfToast? toastObj;
    private async void fetchTOItems()
    {
        try
        {
            if (newIstDto.TONumber == "" || newIstDto.TONumber == null)
            {
                await sfDialogService.AlertAsync("TO Number is required.", "Info");
                return;
            }

            string msg = await istService.checkTONoIsExist(newIstDto.TONumber);
            if (msg != "OK")
            {
                await sfDialogService.AlertAsync(msg, "Info");
                return;
            }

            newIstDto.istDetailDtos = await istService.getISTItems(newIstDto.IstId ?? 0);
            List<ISTDetailDTO> tempIstDet = await istService.fetchTOItems(newIstDto.TONumber!, newIstDto.istDetailDtos);
            newIstDto.istDetailDtos = new(tempIstDet);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void ShowToast(string title, string message, string type)
    {
        if (toastObj == null) return;

        var toast = new ToastModel
            {
                Title = title,
                Content = message,
                CssClass = type == "error" ? "e-toast-danger" : "e-toast-info",
                ShowCloseButton = true,
                Timeout = 5000
            };

        await toastObj.ShowAsync(toast);
    }

    private async void saveTOItems(string pTOStatus)
    {
        try
        {
            if (newIstDto != null)
            {
                if (newIstDto.TONumber == "" || newIstDto.TONumber == null)
                {
                    await sfDialogService.AlertAsync("TO Number is required.", "Info");
                    return;
                }

                if (!newIstDto.istDetailDtos.Any())
                {
                    await sfDialogService.AlertAsync("TO Items is required.", "Info");
                    return;
                }

                bool isISTAvailable = await istService.getISTStatus(newIstDto.IstId ?? 0);
                if (isISTAvailable == false)
                {
                    await sfDialogService.AlertAsync("IST is already closed.", "Info");
                    return;
                }

                closeBox = true;
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void dispatchedToItems(ISTDTO pIstMasterDto, string toNumber, int storeBatchId)
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to Dispatched TO Number?", "Confirmation");
            if (result)
            {
                await istService.dispatchedTONumber(pIstMasterDto, toNumber, storeBatchId, loginId);
                LoadIST();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void markISTClose(ISTDTO pIstMasterDto)
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to close IST,?", "Confirmation");
            if (result)
            {
                if (pIstMasterDto.istStoreBatch.Any(x => x.storeTOStatus == "DRAFT"))
                {
                    var msgToNumber = pIstMasterDto.istStoreBatch.FirstOrDefault(x => x.storeTOStatus == "DRAFT")!.StoreBatchTonumber.ToString();
                    await sfDialogService.AlertAsync($"TO #: {msgToNumber} is in draft mode; you cannot close it.", "Information");
                    return;
                }
                await istService.markCloseIST(pIstMasterDto, loginId);
                LoadIST();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");

        }
    }

    private async void removeTOData(ISTDTO pIstMasterDto, int storeBatchId)
    {
        try
        {
            var result = await sfDialogService.ConfirmAsync("Are you sure you want to remove TO#,?", "Confirmation");
            if (result)
            {
                await istService.removeTONo(pIstMasterDto, storeBatchId, loginId);
                LoadIST();
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

}
