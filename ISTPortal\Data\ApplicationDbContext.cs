﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using ISTPortal.Models;
using Microsoft.EntityFrameworkCore;

namespace ISTPortal.Data;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Ist> Ists { get; set; }

    public virtual DbSet<IstDetail> IstDetails { get; set; }

    public virtual DbSet<IstProgress> IstProgresses { get; set; }

    public virtual DbSet<storeProgress> storeProgresses { get;set; }

    public virtual DbSet<IstStep> IstSteps { get; set; }
    public virtual DbSet<Menu> Menus { get; set; }

    public virtual DbSet<Store> Stores { get; set; }

    public virtual DbSet<StoreBatch> StoreBatches { get; set; }

    public virtual DbSet<StoreBatchDetail> StoreBatchDetails { get; set; }

    public virtual DbSet<StoreBatchDetailsCwh> StoreBatchDetailsCwhs { get; set; }

    public virtual DbSet<StoreBatchDetailsDest> StoreBatchDetailsDests { get; set; }

    public virtual DbSet<StoreBatchesCwh> StoreBatchesCwhs { get; set; }

    public virtual DbSet<StoreBatchesDest> StoreBatchesDests { get; set; }

    public virtual DbSet<User> Users { get; set; }
    public virtual DbSet<UserMenu> UserMenus { get; set; }


    public virtual DbSet<UserStore> UserStores { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Ist>(entity =>
        {
            entity.ToTable("IST");

            entity.Property(e => e.IstId).HasColumnName("istID");
            entity.Property(e => e.IstCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("istCode");
            entity.Property(e => e.IstCreatedBy).HasColumnName("istCreatedBy");
            entity.Property(e => e.IstCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("istCreatedDate");
            entity.Property(e => e.IstDescription)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("istDescription");
            entity.Property(e => e.IstFromStoreId).HasColumnName("istFromStoreID");
            entity.Property(e => e.IstIsActive).HasColumnName("istIsActive");
            entity.Property(e => e.IstModifiedBy).HasColumnName("istModifiedBy");
            entity.Property(e => e.IstModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("istModifiedDate");
            entity.Property(e => e.IstTitle)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("istTitle");
            entity.Property(e => e.istPriority)
               .HasMaxLength(50)
               .IsUnicode(false)
               .HasColumnName("istPriority");
            entity.Property(e => e.istInputMode)
               .HasMaxLength(50)
               .IsUnicode(false)
               .HasColumnName("istInputMode");
            entity.Property(e => e.IstToStoreId).HasColumnName("istToStoreID");
        });

        modelBuilder.Entity<IstDetail>(entity =>
        {
            entity.HasKey(e => e.IstDetailId).HasName("PK_IST_ITEMS");

            entity.ToTable("IST_DETAILS");

            entity.Property(e => e.IstDetailId).HasColumnName("istDetailID");
            entity.Property(e => e.IstDetailCreatedBy).HasColumnName("istDetailCreatedBy");
            entity.Property(e => e.IstDetailCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("istDetailCreatedDate");
            entity.Property(e => e.IstDetailIsActive).HasColumnName("istDetailIsActive");
            entity.Property(e => e.IstDetailModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("istDetailModifiedDate");
            entity.Property(e => e.IstDetailModifyBy).HasColumnName("istDetailModifyBy");
            entity.Property(e => e.IstId).HasColumnName("istID");
            entity.Property(e => e.ItemCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("itemCode");
            entity.Property(e => e.ItemId).HasColumnName("ItemID");
            entity.Property(e => e.ItemMasterCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("itemMasterCode");
            entity.Property(e => e.ItemName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("itemName");
            entity.Property(e => e.ItemQty).HasColumnName("itemQty");
            entity.Property(e => e.istOnHandQty).HasColumnName("istOnHandQty");

       });


        modelBuilder.Entity<IstProgress>(entity =>
        {
            entity.ToTable("IST_PROGRESS");

            entity.Property(e => e.IstProgressId).HasColumnName("istProgressID");
            entity.Property(e => e.IstId).HasColumnName("istID");
            entity.Property(e => e.IstProgressCreatedBy).HasColumnName("istProgressCreatedBy");
            entity.Property(e => e.IstProgressCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("istProgressCreatedDate");
            entity.Property(e => e.IstProgressIsActive).HasColumnName("istProgressIsActive");
            entity.Property(e => e.IstProgressModifiedBy).HasColumnName("istProgressModifiedBy");
            entity.Property(e => e.IstProgressModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("istProgressModifiedDate");
            entity.Property(e => e.IstProgressName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("istProgressName");
            entity.Property(e => e.IstProgressRemarks)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("istProgressRemarks");
            entity.Property(e => e.IstStepId).HasColumnName("istStepID");
        });

        modelBuilder.Entity<storeProgress>(entity =>
        {
            entity.ToTable("STORE_PROGRESS");

            entity.Property(e => e.storeProgressID).HasColumnName("storeProgressID");
            entity.Property(e => e.storeBatchId).HasColumnName("storeBatchId");
            entity.Property(e => e.storeProgressCreatedBy).HasColumnName("storeProgressCreatedBy");
            entity.Property(e => e.storeProgressCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("storeProgressCreatedDate");
            entity.Property(e => e.storeProgressIsActive).HasColumnName("storeProgressIsActive");
            entity.Property(e => e.storeProgressModifiedBy).HasColumnName("storeProgressModifiedBy");
            entity.Property(e => e.storeProgressModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("storeProgressModifiedDate");
            entity.Property(e => e.storeProgressName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("storeProgressName");
            entity.Property(e => e.storeOwnerType)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("storeOwnerType");
        });
        

        modelBuilder.Entity<IstStep>(entity =>
        {
            entity.ToTable("IST_STEPS");

            entity.Property(e => e.IstStepId).HasColumnName("istStepID");
            entity.Property(e => e.IstStepCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("istStepCode");
            entity.Property(e => e.IstStepCreatedBy).HasColumnName("istStepCreatedBy");
            entity.Property(e => e.IstStepCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("istStepCreatedDate");
            entity.Property(e => e.IstStepDesc)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("istStepDesc");
            entity.Property(e => e.IstStepIsActive).HasColumnName("istStepIsActive");
            entity.Property(e => e.IstStepModifiedBy).HasColumnName("istStepModifiedBy");
            entity.Property(e => e.IstStepModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("istStepModifiedDate");
            entity.Property(e => e.IstStepName)
                .HasMaxLength(250)
                .IsUnicode(false)
                .HasColumnName("istStepName");
            entity.Property(e => e.IstStepOwner)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("istStepOwner");
            entity.Property(e => e.IstStepSortOrder).HasColumnName("istStepSortOrder");
        });


        modelBuilder.Entity<Menu>(entity =>
        {
            entity.HasKey(e => e.MenuId);

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.MenuTitle)
                .IsRequired()
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.MenuUrl)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("MenuURL");
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.ParentMenu).WithMany(p => p.InverseParentMenu)
                .HasForeignKey(d => d.ParentMenuId);
                
        });


        modelBuilder.Entity<Store>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_tblStores");

            entity.Property(e => e.Address)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ContactNumber)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Manager)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.Notes)
                .HasMaxLength(500)
                .IsUnicode(false);
        });

        modelBuilder.Entity<StoreBatch>(entity =>
        {
            entity.HasKey(e => e.StoreBatchId).HasName("PK_SRC_STORE_BATCHES");

            entity.ToTable("STORE_BATCHES");

            entity.Property(e => e.StoreBatchId).HasColumnName("storeBatchID");
            entity.Property(e => e.IstId).HasColumnName("istID");
            entity.Property(e => e.StoreBatchCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("storeBatchCode");
            entity.Property(e => e.StoreBatchCreatedBy).HasColumnName("storeBatchCreatedBy");
            entity.Property(e => e.StoreBatchCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("storeBatchCreatedDate");
            entity.Property(e => e.StoreBatchIsActive).HasColumnName("storeBatchIsActive");
            entity.Property(e => e.StoreBatchModifiedBy).HasColumnName("storeBatchModifiedBy");
            entity.Property(e => e.StoreBatchModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("storeBatchModifiedDate");
            entity.Property(e => e.StoreBatchOwnerCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("BP-1, BP-2 | CWH | BP-5, NEEM")
                .HasColumnName("storeBatchOwnerCode");
            entity.Property(e => e.StoreBatchOwnerType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasComment("SRC | CWH | DEST")
                .HasColumnName("storeBatchOwnerType");
            entity.Property(e => e.StoreBatchTonumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("storeBatchTONumber");
            entity.Property(e => e.StoreMasterBatchCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("storeMasterBatchCode");
            entity.Property(e => e.StoreTostatus)
                .HasMaxLength(50)
                .HasColumnName("storeTOStatus");

            entity.Property(e => e.TOCreatedDateAX)
               .HasColumnType("datetime")
               .HasColumnName("storeTOCreatedDateAX");

            entity.Property(e => e.TOShippedDateAX)
               .HasColumnType("datetime")
               .HasColumnName("storeTOShippedDateAX");

            entity.Property(e => e.TOReceivedDateAX)
               .HasColumnType("datetime")
               .HasColumnName("storeTOReceivedDateAX");
        });

        modelBuilder.Entity<StoreBatchDetail>(entity =>
        {
            entity.HasKey(e => e.StoreBatchDetailId).HasName("PK_SRC_STORE_BATCH_DETAILS");

            entity.ToTable("STORE_BATCH_DETAILS");

            entity.Property(e => e.StoreBatchDetailId).HasColumnName("storeBatchDetailID");
            entity.Property(e => e.ItemCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ItemId).HasColumnName("ItemID");
            entity.Property(e => e.ItemMasterCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ItemName)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.StoreBatchDetailCreatedBy).HasColumnName("storeBatchDetailCreatedBy");
            entity.Property(e => e.StoreBatchDetailCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("storeBatchDetailCreatedDate");
            entity.Property(e => e.StoreBatchDetailIsActive).HasColumnName("storeBatchDetailIsActive");
            entity.Property(e => e.StoreBatchDetailModifiedBy).HasColumnName("storeBatchDetailModifiedBy");
            entity.Property(e => e.StoreBatchDetailModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("storeBatchDetailModifiedDate");
            entity.Property(e => e.StoreBatchDetailRemarks)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("storeBatchDetailRemarks");
            entity.Property(e => e.StoreBatchId).HasColumnName("storeBatchID");
            entity.Property(e => e.storeItemOnHandQtyAX).HasColumnName("storeItemOnHandQtyAX");
        });

        modelBuilder.Entity<StoreBatchDetailsCwh>(entity =>
        {
            entity.HasKey(e => e.CwhBatchDetailId);

            entity.ToTable("STORE_BATCH_DETAILS_CWH");

            entity.Property(e => e.CwhBatchDetailId).HasColumnName("cwhBatchDetailID");
            entity.Property(e => e.CwhBatchDetailCreatedBy).HasColumnName("cwhBatchDetailCreatedBy");
            entity.Property(e => e.CwhBatchDetailCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("cwhBatchDetailCreatedDate");
            entity.Property(e => e.CwhBatchDetailIsActive).HasColumnName("cwhBatchDetailIsActive");
            entity.Property(e => e.CwhBatchDetailModifiedBy).HasColumnName("cwhBatchDetailModifiedBy");
            entity.Property(e => e.CwhBatchDetailModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("cwhBatchDetailModifiedDate");
            entity.Property(e => e.CwhBatchDetailRemarks)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("cwhBatchDetailRemarks");
            entity.Property(e => e.CwhBatchId).HasColumnName("cwhBatchID");
            entity.Property(e => e.ItemCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ItemId).HasColumnName("ItemID");
            entity.Property(e => e.ItemMasterCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ItemName)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.cwhItemOnHandQtyAX).HasColumnName("cwhItemOnHandQtyAX");

        });

        modelBuilder.Entity<StoreBatchDetailsDest>(entity =>
        {
            entity.HasKey(e => e.DestBatchDetailId);

            entity.ToTable("STORE_BATCH_DETAILS_DEST");

            entity.Property(e => e.DestBatchDetailId).HasColumnName("destBatchDetailID");
            entity.Property(e => e.DestBatchDetailCreatedBy).HasColumnName("destBatchDetailCreatedBy");
            entity.Property(e => e.DestBatchDetailCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("destBatchDetailCreatedDate");
            entity.Property(e => e.DestBatchDetailIsActive).HasColumnName("destBatchDetailIsActive");
            entity.Property(e => e.DestBatchDetailModifiedBy).HasColumnName("destBatchDetailModifiedBy");
            entity.Property(e => e.DestBatchDetailModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("destBatchDetailModifiedDate");
            entity.Property(e => e.DestBatchDetailRemarks)
                .HasMaxLength(500)
                .IsUnicode(false)
                .HasColumnName("destBatchDetailRemarks");
            entity.Property(e => e.DestBatchId).HasColumnName("destBatchID");
            entity.Property(e => e.ItemCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ItemId).HasColumnName("ItemID");
            entity.Property(e => e.ItemMasterCode)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.ItemName)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.destItemOnHandQtyAX).HasColumnName("destItemOnHandQtyAX");
        });

        modelBuilder.Entity<StoreBatchesCwh>(entity =>
        {
            entity.HasKey(e => e.CwhBatchId);

            entity.ToTable("STORE_BATCHES_CWH");

            entity.Property(e => e.CwhBatchId).HasColumnName("cwhBatchID");
            entity.Property(e => e.CwhBatchCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("cwhBatchCode");
            entity.Property(e => e.CwhBatchCreatedBy).HasColumnName("cwhBatchCreatedBy");
            entity.Property(e => e.CwhBatchCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("cwhBatchCreatedDate");
            entity.Property(e => e.CwhBatchIsActive).HasColumnName("cwhBatchIsActive");
            entity.Property(e => e.CwhBatchModifiedBy).HasColumnName("cwhBatchModifiedBy");
            entity.Property(e => e.CwhBatchModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("cwhBatchModifiedDate");
            entity.Property(e => e.CwhBatchOwnerCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("cwhBatchOwnerCode");
            entity.Property(e => e.CwhBatchOwnerType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("cwhBatchOwnerType");
            entity.Property(e => e.CwhBatchTonumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("cwhBatchTONumber");
            entity.Property(e => e.CwhMasterBatchCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("cwhMasterBatchCode");
            entity.Property(e => e.CwhTostatus)
                .HasMaxLength(50)
                .HasColumnName("cwhTOStatus");
            entity.Property(e => e.IstId).HasColumnName("istID");

            entity.Property(e => e.cwhTOCreatedDateAX)
              .HasColumnType("datetime")
              .HasColumnName("cwhTOCreatedDateAX");

            entity.Property(e => e.cwhTOShippedDateAX)
              .HasColumnType("datetime")
              .HasColumnName("cwhTOShippedDateAX");

            entity.Property(e => e.cwhTOReceivedDateAX)
              .HasColumnType("datetime")
              .HasColumnName("cwhTOReceivedDateAX");

        });

        modelBuilder.Entity<StoreBatchesDest>(entity =>
        {
            entity.HasKey(e => e.DestBatchId);

            entity.ToTable("STORE_BATCHES_DEST");

            entity.Property(e => e.DestBatchId).HasColumnName("destBatchID");
            entity.Property(e => e.DestBatchCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("destBatchCode");
            entity.Property(e => e.DestBatchCreatedBy).HasColumnName("destBatchCreatedBy");
            entity.Property(e => e.DestBatchCreatedDate)
                .HasColumnType("datetime")
                .HasColumnName("destBatchCreatedDate");
            entity.Property(e => e.DestBatchIsActive).HasColumnName("destBatchIsActive");
            entity.Property(e => e.DestBatchModifiedBy).HasColumnName("destBatchModifiedBy");
            entity.Property(e => e.DestBatchModifiedDate)
                .HasColumnType("datetime")
                .HasColumnName("destBatchModifiedDate");
            entity.Property(e => e.DestBatchOwnerCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("destBatchOwnerCode");
            entity.Property(e => e.DestBatchOwnerType)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("destBatchOwnerType");
            entity.Property(e => e.DestBatchTonumber)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("destBatchTONumber");
            entity.Property(e => e.DestMasterBatchCode)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("destMasterBatchCode");
            entity.Property(e => e.DestTostatus)
                .HasMaxLength(50)
                .HasColumnName("destTOStatus");
            entity.Property(e => e.IstId).HasColumnName("istID");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Users__1788CC4C9FD94A01");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FullName)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedBy)
                .HasMaxLength(400)
                .IsUnicode(false);
            entity.Property(e => e.Password)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.UserId)
                .IsRequired()
                .HasMaxLength(400)
                .IsUnicode(false);
        });


        modelBuilder.Entity<UserMenu>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.MenuId });

            entity.HasOne(d => d.Menu).WithMany(p => p.UserMenus)
                .HasForeignKey(d => d.MenuId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasOne(d => d.User).WithMany(p => p.UserMenus)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });


        modelBuilder.Entity<UserStore>(entity =>
        {
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedBy).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}