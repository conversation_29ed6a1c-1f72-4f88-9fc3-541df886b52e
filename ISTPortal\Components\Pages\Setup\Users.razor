﻿@page "/setup/users"

@using ISTPortal.DTO
@using ISTPortal.Services

@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.TreeGrid
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Navigations
@inject UserService UserService
@inject StoreService StoreService
@inject ISTPortal.Services.MenuService MenuService
@inject UserRightsService UserRightsService
@inject SfDialogService DialogService
@rendermode InteractiveServer

<PageTitle>User Management</PageTitle>


<div class="">
    <div class="row">
        <div class="col-12">
            <div class="card" style="height: calc(100vh - 75px); overflow:hidden">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">User Management</h4>
                    <SfButton CssClass="e-primary" @onclick="OpenAddDialog">
                        <i class="fas fa-plus"></i> Add User
                    </SfButton>
                </div>
                <div class="card-body">
                    <SfGrid DataSource="@users" AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                            AllowResizing="true" Height="calc(100vh - 200px)" >
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridPageSettings PageSize="20"></GridPageSettings>
                        
                        <GridColumns>
                            <GridColumn Field="@nameof(UserDTO.UserId)" HeaderText="User ID" Width="150"></GridColumn>
                            <GridColumn Field="@nameof(UserDTO.FullName)" HeaderText="Full Name" Width="200"></GridColumn>
                            <GridColumn Field="@nameof(UserDTO.Status)" HeaderText="Status" Width="80"></GridColumn>
                            <GridColumn HeaderText="Actions" Width="200" AllowFiltering="false" AllowSorting="false">
                                <Template>
                                    @{
                                        var user = (context as UserDTO);
                                    }
                                    <div class="btn-group">
                                        <SfButton CssClass="e-small e-info" @onclick="() => OpenEditDialog(user)">
                                            Edit
                                        </SfButton>
                                        <SfButton CssClass="e-small e-success" @onclick="() => OpenRightsDialog(user)">
                                            Rights
                                        </SfButton>
                                        <SfButton CssClass="e-small e-danger" @onclick="() => DeleteUser(user)">
                                            Delete
                                        </SfButton>
                                    </div>
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Form Dialog -->
<SfDialog @ref="userDialog" Header="@dialogTitle" Width="500px" 
          IsModal="true" @bind-Visible="isUserDialogVisible" ShowCloseIcon="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentUser" OnValidSubmit="@SaveUser">
                <DataAnnotationsValidator />
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">User ID <span class="text-danger">*</span></label>
                            <SfTextBox @bind-Value="currentUser.UserId" Placeholder="Enter user ID"></SfTextBox>
                            <ValidationMessage For="@(() => currentUser.UserId)" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Full Name <span class="text-danger">*</span></label>
                            <SfTextBox @bind-Value="currentUser.FullName" Placeholder="Enter full name"></SfTextBox>
                            <ValidationMessage For="@(() => currentUser.FullName)" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group mb-3">
                            <label class="form-label">Password @(isEditMode ? "" : "*")</label>
                            <SfTextBox @bind-Value="currentUser.Password" Type="InputType.Password"
                                     Placeholder="@(isEditMode ? "Leave blank to keep current password" : "Enter password")"></SfTextBox>
                            @if (!isEditMode)
                            {
                                <ValidationMessage For="@(() => currentUser.Password)" />
                            }
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <SfCheckBox @bind-Checked="currentUser.IsActive" Label="Active"></SfCheckBox>
                        </div>
                    </div>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton Content="Save" IsPrimary="true" OnClick="@SaveUser" />
        <DialogButton Content="Cancel" OnClick="@CloseUserDialog" />
    </DialogButtons>
</SfDialog>

<!-- User Rights Dialog -->
<SfDialog @ref="rightsDialog" Header="@rightsDialogTitle" Width="900px" 
          IsModal="true" @bind-Visible="isRightsDialogVisible" ShowCloseIcon="true">
    <DialogTemplates>
        <Content>
            <SfTab>
                <TabItems>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="Store Rights"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="p-3">
                                <h5>Assign Stores to User</h5>
                                <SfGrid DataSource="@availableStores" AllowSelection="true"
                                       @ref="storeGrid" Height="400">
                                    <GridSelectionSettings Type="Syncfusion.Blazor.Grids.SelectionType.Multiple"
                                                         CheckboxOnly="true" PersistSelection="true"></GridSelectionSettings>
                                    <GridColumns>
                                        <GridColumn Type="ColumnType.CheckBox" Width="50"></GridColumn>
                                        <GridColumn Field="@nameof(StoreDTO.Code)" HeaderText="Code" Width="120"></GridColumn>
                                        <GridColumn Field="@nameof(StoreDTO.Name)" HeaderText="Name" Width="200"></GridColumn>
                                        <GridColumn Field="@nameof(StoreDTO.Manager)" HeaderText="Manager" Width="150"></GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="Menu Rights"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="p-3">
                                <h5>Assign Menus to User</h5>
                                <SfTreeGrid DataSource="@availableMenus" IdMapping="MenuId" ParentIdMapping="ParentMenuId"
                                           TreeColumnIndex="1" AllowSelection="true" @ref="menuTreeGrid" Height="400">
                                    <TreeGridSelectionSettings Type="Syncfusion.Blazor.Grids.SelectionType.Multiple"
                                                             CheckboxOnly="true" PersistSelection="true"></TreeGridSelectionSettings>
                                    <TreeGridColumns>
                                        <TreeGridColumn Type="Syncfusion.Blazor.Grids.ColumnType.CheckBox" Width="50"></TreeGridColumn>
                                        <TreeGridColumn Field="@nameof(MenuDTO.MenuTitle)" HeaderText="Menu Title" Width="250"></TreeGridColumn>
                                        <TreeGridColumn Field="@nameof(MenuDTO.MenuUrl)" HeaderText="URL" Width="200"></TreeGridColumn>
                                    </TreeGridColumns>
                                </SfTreeGrid>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                </TabItems>
            </SfTab>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton Content="Save Rights" IsPrimary="true" OnClick="@SaveUserRights" />
        <DialogButton Content="Cancel" OnClick="@CloseRightsDialog" />
    </DialogButtons>
</SfDialog>

@code {
    private List<UserDTO> users = new();
    private List<StoreDTO> availableStores = new();
    private List<MenuDTO> availableMenus = new();
    private UserDTO currentUser = new();
    private UserDTO currentRightsUser = new();

    private SfDialog userDialog = new();
    private SfDialog rightsDialog = new();
    private SfGrid<StoreDTO> storeGrid = new();
    private SfTreeGrid<MenuDTO> menuTreeGrid = new();

    private bool isUserDialogVisible = false;
    private bool isRightsDialogVisible = false;
    private string dialogTitle = "";
    private string rightsDialogTitle = "";
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
        await LoadAvailableStores();
        await LoadAvailableMenus();
    }

    private async Task LoadUsers()
    {
        users = await UserService.GetAllUsersAsync();
        StateHasChanged();
    }

    private async Task LoadAvailableStores()
    {
        availableStores = await UserRightsService.GetAvailableStoresAsync();
        StateHasChanged();
    }

    private async Task LoadAvailableMenus()
    {
        availableMenus = await UserRightsService.GetAvailableMenusForTreeAsync();
        StateHasChanged();
    }

    private void OpenAddDialog()
    {
        currentUser = new UserDTO { IsActive = true };
        dialogTitle = "Add New User";
        isEditMode = false;
        isUserDialogVisible = true;
    }

    private async Task OpenEditDialog(UserDTO user)
    {
        if (user == null) return;

        var userDetails = await UserService.GetUserByIdAsync(user.Id);
        if (userDetails != null)
        {
            currentUser = new UserDTO
            {
                Id = userDetails.Id,
                UserId = userDetails.UserId,
                FullName = userDetails.FullName,
                IsActive = userDetails.IsActive,
                Password = "" // Don't populate password
            };
        }

        dialogTitle = "Edit User";
        isEditMode = true;
        isUserDialogVisible = true;
    }

    private async Task OpenRightsDialog(UserDTO user)
    {
        if (user == null) return;

        currentRightsUser = user;
        rightsDialogTitle = $"Manage Rights - {user.FullName}";

        // Load user's current assignments
        var assignedStoreIds = await UserRightsService.GetUserAssignedStoreIdsAsync(user.Id);
        var assignedMenuIds = await UserRightsService.GetUserAssignedMenuIdsAsync(user.Id);

        isRightsDialogVisible = true;

        // Wait for the dialog to render completely, then select the assigned items
        await Task.Delay(500);
        StateHasChanged();

        try
        {
            // Select assigned stores with proper validation
            if (storeGrid != null && assignedStoreIds != null && assignedStoreIds.Any() && availableStores != null && availableStores.Any())
            {
                var storeIndices = new List<int>();
                for (int i = 0; i < availableStores.Count; i++)
                {
                    if (assignedStoreIds.Contains(availableStores[i].Id))
                    {
                        storeIndices.Add(i);
                    }
                }

                if (storeIndices.Any())
                {
                    //await storeGrid.SelectRowsAsync(storeIndices.ToArray());
                }
            }
        }
        catch (Exception ex)
        {
            // Log error but don't break the flow
            Console.WriteLine($"Error selecting store rows: {ex.Message}");
        }

        try
        {
            // Select assigned menus with proper validation
            if (menuTreeGrid != null && assignedMenuIds != null && assignedMenuIds.Any() && availableMenus != null && availableMenus.Any())
            {
                var menuIndices = new List<int>();
                for (int i = 0; i < availableMenus.Count; i++)
                {
                    if (assignedMenuIds.Contains(availableMenus[i].MenuId))
                    {
                        menuIndices.Add(i);
                    }
                }

                if (menuIndices.Any())
                {
                    //await menuTreeGrid.SelectRowsAsync(menuIndices.ToArray());
                }
            }
        }
        catch (Exception ex)
        {
            // Log error but don't break the flow
            Console.WriteLine($"Error selecting menu rows: {ex.Message}");
        }
    }

    private async Task SaveUser()
    {
        try
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(currentUser.UserId))
            {
                await DialogService.AlertAsync("User ID is required.", "Validation Error");
                return;
            }

            if (string.IsNullOrWhiteSpace(currentUser.FullName))
            {
                await DialogService.AlertAsync("Full name is required.", "Validation Error");
                return;
            }

            if (!isEditMode && string.IsNullOrWhiteSpace(currentUser.Password))
            {
                await DialogService.AlertAsync("Password is required for new users.", "Validation Error");
                return;
            }

            // Check for duplicates
            if (!isEditMode)
            {
                if (await UserService.UserIdExistsAsync(currentUser.UserId))
                {
                    await DialogService.AlertAsync("User ID already exists.", "Validation Error");
                    return;
                }
            }
            else
            {
                if (await UserService.UserIdExistsAsync(currentUser.UserId, currentUser.Id))
                {
                    await DialogService.AlertAsync("User ID already exists.", "Validation Error");
                    return;
                }
            }

            bool success;
            if (isEditMode)
            {
                success = await UserService.UpdateUserAsync(currentUser, "admin"); // TODO: Get current user
            }
            else
            {
                success = await UserService.CreateUserAsync(currentUser, "admin"); // TODO: Get current user
            }

            if (success)
            {
                await DialogService.AlertAsync($"User {(isEditMode ? "updated" : "created")} successfully.", "Success");
                CloseUserDialog();
                await LoadUsers();
            }
            else
            {
                await DialogService.AlertAsync($"Failed to {(isEditMode ? "update" : "create")} user.", "Error");
            }
        }
        catch (Exception ex)
        {
            await DialogService.AlertAsync($"An error occurred: {ex.Message}", "Error");
        }
    }

    private async Task SaveUserRights()
    {
        List<int> selectedStoreIndices = new();
        List<int> selectedStoreIds = new();
        List<int> selectedMenuIndices = new();
        List<int> selectedMenuIds = new();
        try
        {
            selectedStoreIndices = await storeGrid.GetSelectedRowIndexesAsync();
            selectedStoreIds = selectedStoreIndices.Select(index => availableStores[index].Id).ToList();
        }
        catch (Exception)
        {

        }

        try
        {
            selectedMenuIndices = await menuTreeGrid.GetSelectedRowIndexesAsync();
            selectedMenuIds = selectedMenuIndices.Select(index => availableMenus[index].MenuId).ToList();
        }
        catch (Exception)
        {

        }

        try
        {
            // Get selected stores


            // Get selected menus


            // Update user with new assignments
            currentRightsUser.AssignedStoreIds = selectedStoreIds;
            currentRightsUser.AssignedMenuIds = selectedMenuIds;

            var success = await UserService.UpdateUserAsync(currentRightsUser, "admin"); // TODO: Get current user

            if (success)
            {
                await DialogService.AlertAsync("User rights updated successfully.", "Success");
                CloseRightsDialog();
                StateHasChanged();
            }
            else
            {
                await DialogService.AlertAsync("Failed to update user rights.", "Error");
            }
        }
        catch (Exception ex)
        {
            await DialogService.AlertAsync($"An error occurred: {ex.Message}", "Error");
        }
    }

    private async Task DeleteUser(UserDTO user)
    {
        if (user == null) return;

        var confirmed = await DialogService.ConfirmAsync($"Are you sure you want to delete user '{user.FullName}'?", "Confirm Delete");
        if (confirmed)
        {
            var success = await UserService.DeleteUserAsync(user.Id, "admin"); // TODO: Get current user
            if (success)
            {
                await DialogService.AlertAsync("User deleted successfully.", "Success");
                await LoadUsers();
            }
            else
            {
                await DialogService.AlertAsync("Failed to delete user.", "Error");
            }
        }
    }

    private void CloseUserDialog()
    {
        isUserDialogVisible = false;
        currentUser = new();
    }

    private async Task CloseRightsDialog()
    {
        
        isRightsDialogVisible = false;
        await rightsDialog.HideAsync();
        currentRightsUser = new();
    }
}

