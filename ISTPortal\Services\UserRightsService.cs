using ISTPortal.Data;
using ISTPortal.DTO;
using Microsoft.EntityFrameworkCore;

namespace ISTPortal.Services
{
    public class UserRightsService
    {
        private readonly ApplicationDbContext _context;

        public UserRightsService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<StoreDTO>> GetAvailableStoresAsync()
        {
            return await _context.Stores
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => new StoreDTO
                {
                    Id = s.Id,
                    Code = s.Code,
                    Name = s.Name,
                    IsActive = s.IsActive
                })
                .ToListAsync();
        }

        public async Task<List<MenuDTO>> GetAvailableMenusForTreeAsync()
        {
            return await _context.Menus
                .Where(m => m.IsActive)
                .OrderBy(m => m.MenuSortOrder)
                .Select(m => new MenuDTO
                {
                    MenuId = m.MenuId,
                    MenuTitle = m.MenuTitle,
                    MenuUrl = m.MenuUrl ?? "",
                    ParentMenuId = m.ParentMenuId,
                    MenuSortOrder = m.MenuSortOrder,
                    IsActive = m.IsActive
                })
                .ToListAsync();
        }

        public async Task<List<UserStoreDTO>> GetUserStoresAsync(int userId)
        {
            return await (from us in _context.UserStores
                         join u in _context.Users on us.UserId equals u.Id
                         join s in _context.Stores on us.StoreId equals s.Id
                         where us.UserId == userId && s.IsActive
                         select new UserStoreDTO
                         {
                             Id = us.Id,
                             UserId = us.UserId ?? 0,
                             StoreId = us.StoreId ?? 0,
                             UserName = u.FullName,
                             StoreName = s.Name,
                             StoreCode = s.Code,
                             IsSelected = true
                         }).ToListAsync();
        }

        public async Task<List<UserMenuDTO>> GetUserMenusAsync(int userId)
        {
            return await (from um in _context.UserMenus
                         join u in _context.Users on um.UserId equals u.Id
                         join m in _context.Menus on um.MenuId equals m.MenuId
                         where um.UserId == userId && m.IsActive
                         select new UserMenuDTO
                         {
                             UserId = um.UserId,
                             MenuId = um.MenuId,
                             UserName = u.FullName,
                             MenuTitle = m.MenuTitle,
                             MenuUrl = m.MenuUrl ?? "",
                             IsSelected = true
                         }).ToListAsync();
        }

        public async Task<List<int>> GetUserAssignedStoreIdsAsync(int userId)
        {
            return await _context.UserStores
                .Where(us => us.UserId == userId)
                .Select(us => us.StoreId ?? 0)
                .ToListAsync();
        }

        public async Task<List<int>> GetUserAssignedMenuIdsAsync(int userId)
        {
            return await _context.UserMenus
                .Where(um => um.UserId == userId)
                .Select(um => um.MenuId)
                .ToListAsync();
        }
    }
}
