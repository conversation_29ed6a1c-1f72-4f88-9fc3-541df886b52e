﻿@page "/planning/istmaster"

@using ISTPortal.DTO
@using ISTPortal.Services
@using Syncfusion.Blazor.Cards
@using Syncfusion.Blazor.Grids
@inject Microsoft.FluentUI.AspNetCore.Components.IDialogService DialogService
@inject Syncfusion.Blazor.Popups.SfDialogService sfDialogService
@inject NavigationManager NavigationManager
@inject ISTDataService istService
@inject IJSRuntime JS

@rendermode InteractiveServer

<style>
    .componentWidth {
        width: -webkit-fill-available;
    }

    label {
        padding-left: 2px;
    }

    .cardrow {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 10px;
        margin: 0px;
        background-color: #036ac4;
        color: white;
    }

    .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .e-grid td.e-active {
        background: powderblue !important;
    }
</style>

<FluentBreadcrumb>
    <FluentBreadcrumbItem Href="/home">
        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" Color="@Color.Neutral" Slot="start" />
    </FluentBreadcrumbItem>
    <FluentBreadcrumbItem Href="/planning/istmaster">
        IST Planning
    </FluentBreadcrumbItem>
</FluentBreadcrumb>

@* <div class="row mt-2">
        <div class="col-2">
            <FluentDatePicker Label="Start Date" AriaLabel="Start Date" Class="componentWidth" @bind-Value="@filter.fromDate" />
        </div>
        <div class="col-2">
            <FluentDatePicker Label="End Date" Class="componentWidth" AriaLabel="End Date" @bind-Value="@filter.toDate" />
        </div>
        <div class="col-3">
            <FluentSelect Label="Source Store" Class="componentWidth" @bind-Value="@filter.fromStoreCode" Required="false" TOption="string">
                @if (fromStores.Any())
                {
                    foreach (var item in fromStores)
                    {
                        <FluentOption Value="@(item.storeCode.ToString())">@item.StoreTitle</FluentOption>
                    }
                }
                <FluentOption Value="-1">-----None-----</FluentOption>
            </FluentSelect>
        </div>
        <div class="col-3">
            <FluentSelect Label="Destination Store" Class="componentWidth" @bind-Value="@filter.toStoreCode" TOption="string">
                @if (toStores.Any())
                {
                    foreach (var item in toStores)
                    {
                        <FluentOption Value="@(item.storeCode.ToString())">@item.StoreTitle</FluentOption>
                    }
                }
                <FluentOption Value="-1">-----None-----</FluentOption>
            </FluentSelect>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-4">
            <FluentTextField Class="componentWidth" @bind-Value="filter.ISTCode" Label="IST Code"></FluentTextField>
        </div>
        <div class="col-md"></div>
    </div> *@

<FluentStack HorizontalGap="15" Class="mt-2">
    @*   <FluentButton IconStart="@(new Icons.Regular.Size16.Search())"
        BackgroundColor="#00c853" Color="white">
            Search
        </FluentButton> *@
    <FluentButton IconStart="@(new Icons.Regular.Size16.ContentView())"
                  Appearance="Appearance.Accent" OnClick="viewIST">
        View IST
    </FluentButton>
    <FluentButton IconStart="@(new Icons.Regular.Size16.Add())"
                  Appearance="Appearance.Accent" OnClick="createIST">
        Create IST
    </FluentButton>
    <FluentButton IconStart="@(new Icons.Regular.Size16.Edit())"
                  Appearance="Appearance.Accent" OnClick="editIST">
        Edit IST
    </FluentButton>
    <FluentButton IconStart="@(new Icons.Regular.Size16.Delete())"
                  Appearance="Appearance.Accent" OnClick="deleteIST">
        Delete IST
    </FluentButton>
    <FluentButton IconStart="@(new Icons.Regular.Size16.Send())"
                  Appearance="Appearance.Accent" OnClick="sendIST">
        Send IST
    </FluentButton>
    <FluentButton IconStart="@(new Icons.Regular.Size16.Send())"
                  Appearance="Appearance.Accent" OnClick="cancelIST">
        Cancel IST
    </FluentButton>
</FluentStack>

<div class="mt-2"></div>

<SfGrid @ref="dgIST"
        DataSource="@istList"
        AllowFiltering="true">
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field=@nameof(ISTDTO.IstCode) HeaderText="IST Code" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.IstTitle) HeaderText="IST Title" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.fromStore) HeaderText="Source" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.toStore) HeaderText="Destination" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.TotalItems) HeaderText="Total Items" Format="N0" TextAlign="TextAlign.Right" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.TotalQty) HeaderText="Total Qty." Format="N0" TextAlign="TextAlign.Right" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.ISTStatus) HeaderText="Status" TextAlign="TextAlign.Center" AutoFit="true" AllowFiltering="false">
            <Template>
                @{
                    var ctx = (context as ISTDTO);
                    if (ctx!.ISTStatus == "DRAFT")
                    {
                        <FluentBadge Appearance="Appearance.Neutral" Fill="highlight" BackgroundColor="#ffd800;" Color="#000">@ctx.ISTStatus</FluentBadge>
                    }
                    else if (ctx!.ISTStatus == "ACCEPT")
                    {
                        <FluentBadge Appearance="Appearance.Accent">SENT</FluentBadge>
                    }
                    else
                    {
                        <FluentBadge Appearance="Appearance.Accent">@ctx.ISTStatus</FluentBadge>
                    }
                }
            </Template>
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.IstCreatedByString) HeaderText="CreatedBy" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.IstCreatedDate) Format="dd-MMM-yy HH:mm" HeaderText="Created Date" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.IstModifiedByString) HeaderText="Modified By" AutoFit="true">
        </GridColumn>
        <GridColumn Field=@nameof(ISTDTO.IstModifiedDate) Format="dd-MMM-yy HH:mm" HeaderText="Modified Date" AutoFit="true">
        </GridColumn>
    </GridColumns>
</SfGrid>

<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="900px" Visible="false" @ref="dlgCreateIST">
    <DialogTemplates>
        <Header>IST Planning</Header>
        <Content>
            <EditForm OnValidSubmit="SaveIST" Model="newIstDto">
                <div class="row">
                    <div class="col-md">
                        <FluentTextField Class="componentWidth" Disabled @bind-Value="newIstDto.IstCode" Label="IST Code"></FluentTextField>
                    </div>
                    <div class="col-md">
                        <FluentTextField Class="componentWidth" @bind-Value="newIstDto.IstTitle" Label="IST Title"></FluentTextField>
                    </div>
                    <div class="col-md">
                        <FluentSelect Label="Source Store" Class="componentWidth" @bind-Value="@newIstDto.fromStoreCode" TOption="string">
                            @if (dlgfromStores.Any())
                            {
                                foreach (var item in dlgfromStores)
                                {
                                    <FluentOption Value="@(item.storeCode.ToString())">@item.StoreTitle</FluentOption>
                                }
                            }
                        </FluentSelect>
                    </div>
                    <div class="col-md">
                        <FluentSelect Label="Destination Store" Class="componentWidth" @bind-Value="@newIstDto.toStoreCode" TOption="string">
                            @if (dlgtoStores.Any())
                            {
                                foreach (var item in dlgtoStores)
                                {
                                    <FluentOption Value="@(item.storeCode.ToString())">@item.StoreTitle</FluentOption>
                                }
                            }
                        </FluentSelect>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-md">
                        <FluentTextArea Label="Remarks" style="width: 100%;" @bind-Value="@newIstDto.IstDescription" Rows="2"></FluentTextArea>
                    </div>
                    <div class="col-md-3">
                        <FluentSelect Label="Priority" Class="componentWidth" @bind-Value="@newIstDto.istPriority" TOption="string">
                            <FluentOption Value="Normal">Normal</FluentOption>
                            <FluentOption Value="Urgent">Urgent</FluentOption>
                            <FluentOption Value="Pull back">Pull back</FluentOption>
                        </FluentSelect>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-md">
                        <FluentRadioGroup @bind-Value="newIstDto.istInputMode" Disabled="@itemModeDisable" Name="Mode" TValue="int" Label="Mode">
                            <FluentRadio Value="1">Excel</FluentRadio>
                            <FluentRadio Value="2">Manual</FluentRadio>
                        </FluentRadioGroup>
                    </div>
                </div>
                @{
                    if (newIstDto.istInputMode == 1)
                    {
                        <div class="row mt-2">
                            <div class="col-md">
                                <SfCard ID="BasicCard">
                                    <FluentButton Id="MyUploadStream" IconStart="@(new Icons.Regular.Size16.Attach())"
                                                  Appearance="Appearance.Accent">
                                        Attach Items
                                    </FluentButton>
                                </SfCard>
                                <FluentInputFile @ref="@myFileByStream"
                                                 AnchorId="MyUploadStream"
                                                 DragDropZoneVisible="false"
                                                 Mode="InputFileMode.Stream"
                                                 Multiple="false"
                                                 MaximumFileSize="@(20 * 1024 * 1024)"
                                                 Accept=".xlsx, .xls"
                                                 OnFileUploaded="@OnFileSelected"
                                                 OnCompleted="@OnCompleted" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="row mt-2">
                            <div class="col-md-2">
                                <FluentTextField Class="componentWidth" @bind-Value="inputItemBarcode" Label="Item Barcode"></FluentTextField>
                            </div>
                            <div class="col-md-1">
                                <FluentNumberField Step="1" Min="0" Class="componentWidth" @bind-Value="inputItemQty" Label="Item Quantity"></FluentNumberField>
                            </div>
                            <div class="col-md">
                                <FluentButton IconStart="@(new Icons.Regular.Size16.Add())" OnClick="AddManualItems"
                                              Appearance="Appearance.Accent" Style="margin-top:24px;" Type="Microsoft.FluentUI.AspNetCore.Components.ButtonType.Button">
                                    Add Item
                                </FluentButton>
                            </div>
                        </div>
                    }
                }
                <div class="row mt-2">
                    <div class="col-md">
                        <SfCard ID="BasicCard">
                            <FluentButton IconStart="@(new Icons.Regular.Size16.ClearFormatting())"
                                          Appearance="Appearance.Accent" OnClick="clearItemLst">
                                Clear Items List
                            </FluentButton>
                        </SfCard>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md">
                        <SfGrid @ref="dgISTDetail"
                                DataSource="@newIstDto.istDetailDtos"
                                AllowFiltering="true" Height="220">
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(ISTDetailDTO.ItemCode) HeaderText="Barcode" AutoFit>
                                </GridColumn>
                                <GridColumn Field=@nameof(ISTDetailDTO.ItemQty) HeaderText="Quantity" TextAlign="TextAlign.Right" AutoFit>
                                </GridColumn>
                                <GridColumn Field=@nameof(ISTDetailDTO.ItemDesc) HeaderText="Desc." TextAlign="TextAlign.Left" AutoFit>
                                </GridColumn>
                                <GridColumn Field=@nameof(ISTDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                                </GridColumn>
                                <GridColumn Field=@nameof(ISTDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                                </GridColumn>
                                <GridColumn Field=@nameof(ISTDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                                </GridColumn>
                                <GridColumn HeaderText=" " TextAlign="TextAlign.Left" AutoFit>
                                    <Template Context="ctxDet">
                                        @{
                                            var ctx = (ctxDet as ISTDetailDTO);
                                            if (newIstDto.istInputMode != 1)
                                            {
                                                // <SfButton Type="Microsoft.FluentUI.AspNetCore.Components.ButtonType.Submit" CssClass="e-small e-danger e-filled" IconCss="e-icons e-delete" OnClick="@(() => removeItem(ctx!))">
                                                //     Remove
                                                // </SfButton>
                                                <FluentButton IconStart="@(new Icons.Regular.Size16.Delete())"
                                                              Appearance="Appearance.Stealth" OnClick="@(() => removeItem(ctx))">
                                                    Remove
                                                </FluentButton>
                                            }
                                        }
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                    <FluentButton IconStart="@(new Icons.Regular.Size16.Save())"
                                  Appearance="Appearance.Accent" Type="Microsoft.FluentUI.AspNetCore.Components.ButtonType.Submit">
                        Save
                    </FluentButton>
                    <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                                  Appearance="Appearance.Accent" OnClick="@(() => dlgCreateIST.HideAsync())">
                        Close
                    </FluentButton>
                </FluentStack>

            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>


<SfDialog ShowCloseIcon="true" CloseOnEscape="true" IsModal="true" Width="750px" Visible="false" @ref="dlgViewIST">
    <DialogTemplates>
        <Header>VIEW - IST Planning</Header>
        <Content>
            <div class="row">
                <div class="col-md">
                    <b>IST Code :</b> @newIstDto.IstCode
                </div>
                <div class="col-md">
                    <b>IST Code :</b> @newIstDto.IstTitle
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Source Store :</b> @newIstDto.fromStore
                </div>
                <div class="col-md">
                    <b>Destination Store :</b> @newIstDto.toStore
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <b>Remarks :</b> @newIstDto.IstDescription
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md">
                    <SfGrid @ref="dgISTDetail"
                            DataSource="@newIstDto.istDetailDtos"
                            AllowFiltering="true" Height="250">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemCode) HeaderText="Item Barcode" Width="200">
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemQty) HeaderText="Item Quantity" TextAlign="TextAlign.Right" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.ItemDesc) HeaderText="Desc." TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR1) HeaderText="HIR1" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR2) HeaderText="HIR2" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                            <GridColumn Field=@nameof(ISTDetailDTO.HIR3) HeaderText="HIR3" TextAlign="TextAlign.Left" AutoFit>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
            </div>

            <FluentStack HorizontalGap="15" Class="mt-2 mb-2">
                <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                              Appearance="Appearance.Accent" OnClick="@(() => dlgViewIST.HideAsync())">
                    Close
                </FluentButton>

                @* <FluentButton IconStart="@(new Icons.Regular.Size16.LockClosed())"
                                  Appearance="Appearance.Accent" OnClick="@(() => sfDialogService.AlertAsync("Testing Inner Dialog","Info"))">
                        Test
                    </FluentButton> *@

            </FluentStack>
        </Content>
    </DialogTemplates>
</SfDialog>


@code {

    public async void removeItem(ISTDetailDTO pISTDET)
    {
        try
        {
            newIstDto.istDetailDtos.Remove(pISTDET);
            newIstDto.istDetailDtos = new List<ISTDetailDTO>(newIstDto.istDetailDtos);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
        }
    }

    public void clearItemLst()
    {
        newIstDto.istDetailDtos = new();
        newIstDto.istDetailDtos.Clear();
        itemModeDisable = false;
    }

    FluentInputFile? myFileByStream = default!;
    int? progressPercent;
    string? progressTitle;
    bool itemModeDisable = false;
    string inputItemBarcode = "";
    int inputItemQty = 1;

    private filterForm filter = new();

    private string loginId = "admin";
    private List<StoresDTO> fromStores = new();
    private List<StoresDTO> toStores = new();

    private List<StoresDTO> dlgfromStores = new();
    private List<StoresDTO> dlgtoStores = new();

    private List<ISTDTO> istList = new();
    private SfGrid<ISTDTO>? dgIST;
    private SfGrid<ISTDetailDTO>? dgISTDetail;
    private SfDialog dlgCreateIST, dlgViewIST;

    private ISTDTO newIstDto = new();

    private bool isProcessingModeChange = false;

    private async void LoadStores()
    {
        fromStores = await istService.getUserStores(loginId);
        toStores = await istService.getUserStores(loginId);
        StateHasChanged();
    }

    private async void LoadIST()
    {
        istList = await istService.getIST(loginId, filter);
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            LoadStores();
            LoadIST();
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message.ToString());
        }
    }

    private async void createIST()
    {
        try
        {
            newIstDto = new();

            dlgfromStores = await istService.getUserStores(loginId);
            dlgtoStores = await istService.getUserStores(loginId);
            newIstDto.fromStoreCode = dlgfromStores.FirstOrDefault()!.storeCode;
            newIstDto.toStoreCode = dlgtoStores.FirstOrDefault()!.storeCode;

            DateTime now = DateTime.Now;
            Random rand = new Random();
            string uniqueCode = (now.Ticks % 100000).ToString() + rand.Next(100, 9999);
            newIstDto.IstCode = "IST-" + uniqueCode;

            itemModeDisable = false;

            await dlgCreateIST.ShowAsync(true);
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void editIST()
    {
        try
        {
            if (dgIST!.SelectedRecords.Count > 0)
            {

                if (dgIST!.SelectedRecords[0].ISTStatus != "DRAFT")
                {
                    await DialogService.ShowInfoAsync($"The selected IST status is '{dgIST!.SelectedRecords[0].ISTStatus}'; you cannot edit it.", "Information");
                    return;
                }

                dlgfromStores = await istService.getUserStores(loginId);
                dlgtoStores = await istService.getUserStores(loginId);

                newIstDto = new();
                newIstDto = dgIST.SelectedRecords[0];
                newIstDto.istDetailDtos = await istService.getISTItems(newIstDto.IstId ?? 0);

                itemModeDisable = true;

                await dlgCreateIST.ShowAsync(true);
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void SaveIST()
    {
        try
        {
            if (newIstDto.IstCode == "" || newIstDto.IstCode == null)
            {
                //await DialogService.ShowErrorAsync("IST Code is required.", "Information");
                await sfDialogService.AlertAsync("IST Code is required.", "Information");
                return;
            }
            if (newIstDto.IstTitle == "" || newIstDto.IstTitle == null)
            {
                //await DialogService.ShowErrorAsync("IST Title is required.", "Information");
                await sfDialogService.AlertAsync("IST Title is required.", "Information");
                return;
            }

            if (newIstDto.fromStoreCode == newIstDto.toStoreCode)
            {
                await sfDialogService.AlertAsync("The source store must be different from the destination store.", "Information");
                return;
            }

            if (!newIstDto.istDetailDtos.Any())
            {
                await sfDialogService.AlertAsync("It is required to attach the item list or add manual item.", "Information");
                //await DialogService.ShowErrorAsync("It is required to attach the item list or add manual item.", "Information");
                return;
            }

            await istService.SaveIST(newIstDto, loginId);

            LoadIST();
            await dlgCreateIST.HideAsync();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async Task OnFileSelected(FluentInputFileEventArgs pFile)
    {
        try
        {
            progressPercent = pFile.ProgressPercent;

            if (pFile == null)
                return;

            using var stream = new MemoryStream();
            await pFile.Stream!.CopyToAsync(stream);

            newIstDto.istDetailDtos = new();
            newIstDto.istDetailDtos = await istService.ValidateExcelFile(stream);
            if (newIstDto.istDetailDtos.Count > 0)
                itemModeDisable = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync("Error processing file: " + ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync("Error processing file: " + ex.Message);
        }
    }
    void OnCompleted(IEnumerable<FluentInputFileEventArgs> files)
    {
        progressPercent = myFileByStream!.ProgressPercent;
    }

    private async void deleteIST()
    {
        try
        {
            if (dgIST!.SelectedRecords.Count > 0)
            {
                if (dgIST!.SelectedRecords[0].ISTStatus != "DRAFT")
                {
                    await sfDialogService.AlertAsync($"The selected IST status is '{dgIST!.SelectedRecords[0].ISTStatus}'; you cannot delete it.", "Information");
                    //await DialogService.ShowInfoAsync($"The selected IST status is '{dgIST!.SelectedRecords[0].ISTStatus}'; you cannot delete it.", "Information");
                    return;
                }

                var result = await sfDialogService.ConfirmAsync("Are you sure you want to delete IST?", "Confirmation");
                if (result)
                {
                    int istId = dgIST.SelectedRecords[0].IstId ?? 0;
                    await istService.deleteIST(istId, loginId);
                    LoadIST();
                }

                // var result = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete IST?");
                // if (result)
                // {
                //     int istId = dgIST.SelectedRecords[0].IstId ?? 0;
                //     await istService.deleteIST(istId, loginId);
                //     LoadIST();
                // }
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void viewIST()
    {
        try
        {
            if (dgIST!.SelectedRecords.Count > 0)
            {
                dlgfromStores = await istService.getUserStores(loginId);
                dlgtoStores = await istService.getUserStores(loginId);

                newIstDto = new();
                newIstDto = dgIST.SelectedRecords[0];
                newIstDto.istDetailDtos = await istService.getISTItems(newIstDto.IstId ?? 0);
                await dlgViewIST.ShowAsync(true);
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void sendIST()
    {
        try
        {
            if (dgIST!.SelectedRecords.Count > 0)
            {
                if (dgIST!.SelectedRecords[0].ISTStatus != "DRAFT")
                {
                    await sfDialogService.AlertAsync($"The selected IST status is already '{dgIST!.SelectedRecords[0].ISTStatus}';", "Information");
                    //await DialogService.ShowInfoAsync($"The selected IST status is already '{dgIST!.SelectedRecords[0].ISTStatus}';", "Information");
                    return;
                }
                var result = await sfDialogService.ConfirmAsync("Are you sure you want to send IST?", "Confirmation");
                if (result)
                {
                    int istId = dgIST.SelectedRecords[0].IstId ?? 0;
                    await istService.sendIST(istId, loginId);
                    LoadIST();
                }

                // var result = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to send IST?");
                // if (result)
                // {
                //     int istId = dgIST.SelectedRecords[0].IstId ?? 0;
                //     await istService.sendIST(istId, loginId);
                //     LoadIST();
                // }
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void cancelIST()
    {
        try
        {
            if (dgIST!.SelectedRecords.Count > 0)
            {
                if (dgIST!.SelectedRecords[0].ISTStatus == "CLOSED")
                {
                    await sfDialogService.AlertAsync($"The selected IST status is '{dgIST!.SelectedRecords[0].ISTStatus}'; you cannot cancel it.", "Information");
                    //await DialogService.ShowInfoAsync($"The selected IST status is '{dgIST!.SelectedRecords[0].ISTStatus}'; you cannot delete it.", "Information");
                    return;
                }

                var result = await sfDialogService.ConfirmAsync("Are you sure you want to cancel IST?", "Confirmation");
                if (result)
                {
                    int istId = dgIST.SelectedRecords[0].IstId ?? 0;
                    await istService.cancelIST(istId, loginId);
                    LoadIST();
                }
            }
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

    private async void AddManualItems()
    {
        try
        {
            if (string.IsNullOrEmpty(inputItemBarcode))
                return;

            if (inputItemQty <= 0)
                return;

            var itemExist = newIstDto.istDetailDtos.Any(x => x.ItemCode == inputItemBarcode);
            if (itemExist)
            {
                await sfDialogService.AlertAsync($"This item already exists. You can remove it and input it again with a new quantity.", "Information");
                return;
            }

            var wmsItem = await istService.getWMSItemInfo(inputItemBarcode);

            ISTDetailDTO detailDTO = new ISTDetailDTO()
            {
                ItemCode = inputItemBarcode,
                ItemQty = inputItemQty,
                ItemMasterCode = inputItemBarcode,
                ItemDesc = wmsItem != null ? wmsItem.ItemDesc : "",
                HIR1 = wmsItem != null ? wmsItem.HIR1 : "",
                HIR2 = wmsItem != null ? wmsItem.HIR2 : "",
                HIR3 = wmsItem != null ? wmsItem.HIR3 : "",
            };
            newIstDto.istDetailDtos = new List<ISTDetailDTO>(newIstDto.istDetailDtos) { detailDTO };

            inputItemBarcode = "";
            inputItemQty = 1;

            if (newIstDto.istDetailDtos.Count > 0)
                itemModeDisable = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await sfDialogService.AlertAsync(ex.Message.ToString(), "Error");
            //await DialogService.ShowErrorAsync(ex.Message.ToString(), "Error");
        }
    }

}
