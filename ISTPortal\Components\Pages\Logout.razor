@page "/logout"
@inject NavigationManager Navigation
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
[CascadingParameter] public HttpContext? HttpContext { get; set; }

@code {
    [CascadingParameter] public HttpContext? HttpContext { get; set; }
    protected override async Task OnInitializedAsync()
    {
        if (HttpContext != null)
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            Navigation.NavigateTo("/login", true);
        }
        else
        {
            Navigation.NavigateTo("/login", true);
        }
    }
}

<!-- Optionally, show a message or spinner while redirecting -->
<div style="text-align:center; margin-top:2rem;">
    Logging out...
</div>
