﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "ApplicationDbContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "ISTPortal",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[IST]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[IST_DETAILS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[IST_PROGRESS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[IST_STEPS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Menus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_BATCH_DETAILS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_BATCH_DETAILS_CWH]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_BATCH_DETAILS_DEST]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_BATCHES]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_BATCHES_CWH]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_BATCHES_DEST]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[STORE_PROGRESS]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Stores]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserMenus]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Users]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserStores]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": false,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": true,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}