using ISTPortal.Components;

using ISTPortal.Data;
using ISTPortal.Models;
using ISTPortal.Services;
using ISTPortal.Services;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.FluentUI.AspNetCore.Components;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Popups;

namespace ISTPortal
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzY2NDQzOUAzMjM4MmUzMDJlMzBkT1liN3Z3aGwzb0c4YmNNNUhOd2NzWHp6RUFVVGVNL0Y1Z2x3bW1FYzJJPQ==");

            // Add services to the container.
            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();

            // Add authentication services for Blazor Server
            //builder.Services.AddAuthenticationCore();
            //builder.Services.AddAuthorizationCore();

            // Add custom authentication services
            //builder.Services.AddScoped<AuthenticationService>();
            //builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthenticationStateProvider>();
            //builder.Services.AddScoped<CustomAuthenticationStateProvider>();

            builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
                .AddCookie(c =>
                {
                    c.Cookie.Name = "auth_token";
                    c.Cookie.MaxAge = TimeSpan.FromMinutes(120);
                    c.LoginPath = "/login";
                    c.AccessDeniedPath = "/account/access-denied";
                });
            builder.Services.AddAuthorization();
            builder.Services.AddCascadingAuthenticationState();



            // Add DbContext
            builder.Services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

            builder.Services.AddFluentUIComponents();
            builder.Services.AddSyncfusionBlazor();

            builder.Services.AddScoped<ISTDataService>();
            builder.Services.AddScoped<SfDialogService>();

            // Setup services
            builder.Services.AddScoped<UserService>();
            builder.Services.AddScoped<Services.MenuService>();
            builder.Services.AddScoped<StoreService>();
            builder.Services.AddScoped<UserRightsService>();
            builder.Services.AddScoped<AuthenticationService>();
            builder.Services.AddHttpContextAccessor();

            var app = builder.Build();

            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();
            
            app.UseAntiforgery();
            app.UseAuthentication();
            app.UseAuthorization();

            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode();

            app.Run();
        }
    }
}
