using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using System.Security.Claims;
using System.Text.Json;
using ISTPortal.Models;

namespace ISTPortal.Services
{
    public class AuthenticationService
    {
        private readonly ProtectedLocalStorage _protectedLocalStorage;
        private const string AuthStorageKey = "ISTPortalAuthData";

        public AuthenticationService(ProtectedLocalStorage protectedLocalStorage)
        {
            _protectedLocalStorage = protectedLocalStorage;
        }

        public async Task<bool> SetAuthenticationAsync(User user)
        {
            try
            {
                var authData = new AuthData
                {
                    UserId = user.UserId,
                    UserDbId = user.Id,
                    FullName = user.FullName,
                    IsActive = user.IsActive,
                    LoginTime = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddHours(8)
                };

                var json = JsonSerializer.Serialize(authData);
                await _protectedLocalStorage.SetAsync(AuthStorageKey, json);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting authentication: {ex.Message}");
                return false;
            }
        }

        public async Task<AuthData?> GetAuthenticationAsync()
        {
            try
            {
                var result = await _protectedLocalStorage.GetAsync<string>(AuthStorageKey);
                if (result.Success && !string.IsNullOrEmpty(result.Value))
                {
                    var authData = JsonSerializer.Deserialize<AuthData>(result.Value);
                    
                    // Check if authentication has expired
                    if (authData != null && authData.ExpiresAt > DateTime.UtcNow)
                    {
                        return authData;
                    }
                    else
                    {
                        // Remove expired authentication
                        await ClearAuthenticationAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting authentication: {ex.Message}");
            }

            return null;
        }

        public async Task<bool> ClearAuthenticationAsync()
        {
            try
            {
                await _protectedLocalStorage.DeleteAsync(AuthStorageKey);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error clearing authentication: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            var authData = await GetAuthenticationAsync();
            return authData != null;
        }

        public async Task<bool> ExtendSessionAsync()
        {
            try
            {
                var authData = await GetAuthenticationAsync();
                if (authData != null)
                {
                    authData.ExpiresAt = DateTime.UtcNow.AddHours(8);
                    var json = JsonSerializer.Serialize(authData);
                    await _protectedLocalStorage.SetAsync(AuthStorageKey, json);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extending session: {ex.Message}");
            }

            return false;
        }
    }

    public class AuthData
    {
        public string UserId { get; set; } = string.Empty;
        public int UserDbId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime LoginTime { get; set; }
        public DateTime ExpiresAt { get; set; }
    }
}
